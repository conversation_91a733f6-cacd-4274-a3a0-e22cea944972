import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { MinimalRowList } from './minimal-row-list'
import { GroupedCategoryList } from './grouped-category-list'
import { StatusFocusedList } from './status-focused-list'
import { ActionOrientedList } from './action-oriented-list'
import { CompactGridList } from './compact-grid-list'

const mockBenefits = [
  {
    id: '1',
    name: 'Bike Leasing (Dienstradleasing)',
    icon: '🚴',
    is_verified: false,
    is_admin_verified: false,
    confirmations_count: 0,
    category: 'transport'
  },
  {
    id: '2',
    name: 'Gym Membership (Fitnessstudio-Mitgliedschaft)',
    icon: '🏋️',
    is_verified: true,
    is_admin_verified: false,
    confirmations_count: 3,
    category: 'health'
  },
  {
    id: '3',
    name: 'Sports Programs (Sportprogramme)',
    icon: '⚽',
    is_verified: true,
    is_admin_verified: false,
    confirmations_count: 1,
    category: 'health'
  },
  {
    id: '4',
    name: 'Wellness Programs (Wellness-Programme)',
    icon: '🧘',
    is_verified: false,
    is_admin_verified: true,
    confirmations_count: 0,
    category: 'wellness'
  },
  {
    id: '5',
    name: 'Company Car (Firmenwagen)',
    icon: '🚗',
    is_verified: true,
    is_admin_verified: false,
    confirmations_count: 2,
    category: 'transport'
  }
]

const mockHandlers = {
  onVerify: (id: string) => console.log(`Verify benefit ${id}`),
  onDeleteOrDispute: (id: string) => {
    const benefit = mockBenefits.find(b => b.id === id)
    if (benefit?.is_verified || benefit?.is_admin_verified) {
      console.log(`Dispute benefit ${id}`)
    } else {
      console.log(`Remove benefit ${id}`)
    }
  }
}

const meta: Meta = {
  title: 'Design Examples/Benefits List Designs',
  parameters: {
    layout: 'padded',
  },
}

export default meta

export const MinimalRowDesign: StoryObj = {
  render: () => (
    <MinimalRowList
      benefits={mockBenefits}
      canManage={true}
      {...mockHandlers}
    />
  ),
}

export const GroupedCategoryDesign: StoryObj = {
  render: () => (
    <GroupedCategoryList
      benefits={mockBenefits}
      canManage={true}
      {...mockHandlers}
    />
  ),
}

export const StatusFocusedDesign: StoryObj = {
  render: () => (
    <StatusFocusedList
      benefits={mockBenefits}
      canManage={true}
      {...mockHandlers}
    />
  ),
}

export const ActionOrientedDesign: StoryObj = {
  render: () => (
    <ActionOrientedList
      benefits={mockBenefits}
      canManage={true}
      {...mockHandlers}
    />
  ),
}

export const CompactGridDesign: StoryObj = {
  render: () => (
    <CompactGridList
      benefits={mockBenefits}
      canManage={true}
      {...mockHandlers}
    />
  ),
}
