'use client'

import { CheckCircle, Shield, Users, Plus, Download, Search } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useState } from 'react'

interface CompanyBenefit {
  id: string
  name: string
  icon?: string
  is_verified: boolean
  is_admin_verified?: boolean
  confirmations_count: number
  category: string
}

interface ActionOrientedListProps {
  benefits: CompanyBenefit[]
  canManage: boolean
  onVerify: (id: string) => void
  onDeleteOrDispute: (id: string) => void
}

export function ActionOrientedList({ benefits, canManage, onVerify, onDeleteOrDispute }: ActionOrientedListProps) {
  const [filter, setFilter] = useState<'all' | 'verified' | 'pending'>('all')
  const [searchTerm, setSearchTerm] = useState('')

  const filteredBenefits = benefits.filter(benefit => {
    const matchesSearch = benefit.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = 
      filter === 'all' || 
      (filter === 'verified' && (benefit.is_verified || benefit.is_admin_verified)) ||
      (filter === 'pending' && !benefit.is_verified && !benefit.is_admin_verified)
    
    return matchesSearch && matchesFilter
  })

  const getQuickAction = (benefit: CompanyBenefit) => {
    if (benefit.is_admin_verified) {
      return {
        label: 'Admin Verified',
        icon: <Shield className="w-3 h-3" />,
        color: 'bg-blue-100 text-blue-800',
        disabled: true
      }
    }
    if (benefit.is_verified) {
      return {
        label: 'User Verified',
        icon: <CheckCircle className="w-3 h-3" />,
        color: 'bg-green-100 text-green-800',
        disabled: true
      }
    }
    return {
      label: 'Verify Now',
      icon: <CheckCircle className="w-3 h-3" />,
      color: 'bg-green-100 text-green-800 hover:bg-green-200',
      action: () => onVerify(benefit.id)
    }
  }

  const getPriorityLevel = (benefit: CompanyBenefit) => {
    if (benefit.is_admin_verified) return 'low'
    if (benefit.is_verified) return 'medium'
    return 'high'
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-l-red-400 bg-red-50'
      case 'medium': return 'border-l-amber-400 bg-amber-50'
      case 'low': return 'border-l-green-400 bg-green-50'
      default: return 'border-l-gray-400 bg-gray-50'
    }
  }

  return (
    <div className="space-y-4">
      {/* Action Header */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Benefits Management</h3>
            <p className="text-sm text-gray-600">Quick actions and bulk operations</p>
          </div>
          
          {canManage && (
            <div className="flex flex-wrap gap-2">
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                <Plus className="w-4 h-4 mr-1" />
                Add Benefits
              </Button>
              <Button size="sm" variant="outline">
                <Download className="w-4 h-4 mr-1" />
                Export
              </Button>
              <Button size="sm" variant="outline">
                Bulk Verify
              </Button>
            </div>
          )}
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-3 mt-4">
          <div className="relative flex-1">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search benefits..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex gap-2">
            {['all', 'verified', 'pending'].map((filterOption) => (
              <Button
                key={filterOption}
                size="sm"
                variant={filter === filterOption ? 'default' : 'outline'}
                onClick={() => setFilter(filterOption as any)}
                className="capitalize"
              >
                {filterOption}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Benefits List */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        {filteredBenefits.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <p>No benefits found matching your criteria</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {filteredBenefits.map((benefit) => {
              const quickAction = getQuickAction(benefit)
              const priority = getPriorityLevel(benefit)
              
              return (
                <div 
                  key={benefit.id} 
                  className={`border-l-4 ${getPriorityColor(priority)} hover:bg-gray-50 transition-colors`}
                >
                  <div className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      {/* Left: Benefit Info */}
                      <div className="flex items-center space-x-4 flex-1 min-w-0">
                        <div className="w-10 h-10 bg-white rounded-lg border border-gray-200 flex items-center justify-center flex-shrink-0">
                          <span className="text-lg">{benefit.icon || '🎯'}</span>
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <h4 className="text-base font-medium text-gray-900 truncate">{benefit.name}</h4>
                            {benefit.is_admin_verified && (
                              <Shield className="w-4 h-4 text-blue-600" title="Admin Verified" />
                            )}
                            {benefit.is_verified && !benefit.is_admin_verified && (
                              <CheckCircle className="w-4 h-4 text-green-600" title="User Verified" />
                            )}
                          </div>
                          
                          <div className="flex items-center space-x-4 mt-1">
                            <span className="text-sm text-gray-500 capitalize">
                              {benefit.category?.replace('_', ' ') || 'Other'}
                            </span>
                            {benefit.confirmations_count > 0 && (
                              <div className="flex items-center space-x-1 text-gray-600">
                                <Users className="w-3 h-3" />
                                <span className="text-sm">{benefit.confirmations_count} confirmations</span>
                              </div>
                            )}
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              priority === 'high' ? 'bg-amber-100 text-amber-800' :
                              priority === 'medium' ? 'bg-green-100 text-green-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {priority === 'high' ? 'Needs Verification' :
                               priority === 'medium' ? 'User Verified' : 'Admin Verified'}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Right: Quick Actions */}
                      <div className="flex items-center space-x-2 ml-4">
                        <Button
                          size="sm"
                          variant={quickAction.disabled ? 'secondary' : 'outline'}
                          onClick={quickAction.action}
                          disabled={quickAction.disabled}
                          className={`${quickAction.color} border-0 text-xs px-3 py-1`}
                        >
                          {quickAction.icon}
                          <span className="ml-1">{quickAction.label}</span>
                        </Button>

                        {canManage && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => onDeleteOrDispute(benefit.id)}
                            className="border-red-300 text-red-700 hover:bg-red-50 text-xs px-3 py-1"
                          >
                            {(benefit.is_verified || benefit.is_admin_verified) ? 'Dispute' : 'Remove'}
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-gray-900">{benefits.length}</div>
          <div className="text-sm text-gray-600">Total Benefits</div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-green-600">
            {benefits.filter(b => b.is_verified || b.is_admin_verified).length}
          </div>
          <div className="text-sm text-gray-600">Verified</div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-amber-600">
            {benefits.filter(b => !b.is_verified && !b.is_admin_verified).length}
          </div>
          <div className="text-sm text-gray-600">Need Action</div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-blue-600">
            {benefits.reduce((sum, b) => sum + b.confirmations_count, 0)}
          </div>
          <div className="text-sm text-gray-600">Confirmations</div>
        </div>
      </div>
    </div>
  )
}
