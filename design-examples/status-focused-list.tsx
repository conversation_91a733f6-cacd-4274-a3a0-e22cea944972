'use client'

import { <PERSON><PERSON><PERSON><PERSON>, Shield, AlertTriangle, Clock, Users, TrendingUp } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface CompanyBenefit {
  id: string
  name: string
  icon?: string
  is_verified: boolean
  is_admin_verified?: boolean
  confirmations_count: number
  category: string
  user_has_verified?: boolean // Whether the current user has verified this benefit
}

interface StatusFocusedListProps {
  benefits: CompanyBenefit[]
  canManage: boolean
  onVerify: (id: string) => void
  onDeleteOrDispute: (id: string) => void
}

export function StatusFocusedList({ benefits, canManage, onVerify, onDeleteOrDispute }: StatusFocusedListProps) {
  // Group benefits by verification status
  // Merge admin and user verified into one "verified" section
  const verified = benefits.filter(b =>
    b.is_admin_verified || (b.is_verified && b.confirmations_count >= 2)
  )
  const partiallyVerified = benefits.filter(b =>
    b.is_verified && !b.is_admin_verified && b.confirmations_count === 1
  )
  const pending = benefits.filter(b =>
    !b.is_verified && !b.is_admin_verified
  )

  const StatusSection = ({ 
    title, 
    benefits, 
    icon, 
    color, 
    bgColor, 
    description 
  }: {
    title: string
    benefits: CompanyBenefit[]
    icon: React.ReactNode
    color: string
    bgColor: string
    description: string
  }) => (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      {/* Section Header */}
      <div className={`${bgColor} px-4 py-3 border-b border-gray-200`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={`${color}`}>{icon}</div>
            <div>
              <h4 className="text-sm font-semibold text-gray-900">{title}</h4>
              <p className="text-xs text-gray-600">{description}</p>
            </div>
          </div>
          <div className={`${color} bg-white px-2 py-1 rounded-full`}>
            <span className="text-sm font-bold">{benefits.length}</span>
          </div>
        </div>
      </div>

      {/* Benefits List */}
      {benefits.length > 0 ? (
        <div className="divide-y divide-gray-100">
          {benefits.map((benefit) => (
            <div key={benefit.id} className="px-4 py-3 hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  <span className="text-lg flex-shrink-0">{benefit.icon || '🎯'}</span>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h5 className="text-sm font-medium text-gray-900 truncate">{benefit.name}</h5>
                      {/* Verification type indicator */}
                      {benefit.is_admin_verified ? (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          <Shield className="w-3 h-3 mr-1" />
                          Admin
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Community
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-3 mt-1">
                      <span className="text-xs text-gray-500 capitalize">
                        {benefit.category?.replace('_', ' ') || 'Other'}
                      </span>
                      {benefit.confirmations_count > 0 && (
                        <div className="flex items-center space-x-1 text-gray-600">
                          <Users className="w-3 h-3" />
                          <span className="text-xs">{benefit.confirmations_count} confirmations</span>
                        </div>
                      )}
                      {/* User's personal verification status */}
                      {!benefit.is_admin_verified && (
                        <span className={`text-xs px-2 py-0.5 rounded-full ${
                          benefit.user_has_verified
                            ? 'bg-green-50 text-green-700'
                            : 'bg-gray-50 text-gray-600'
                        }`}>
                          {benefit.user_has_verified ? '✓ You verified' : 'Not verified by you'}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  {/* Show verify button for non-admin verified benefits where user hasn't verified */}
                  {!benefit.is_admin_verified && !benefit.user_has_verified && (
                    <Button
                      size="sm"
                      onClick={() => onVerify(benefit.id)}
                      className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1"
                    >
                      Verify
                    </Button>
                  )}

                  {canManage && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onDeleteOrDispute(benefit.id)}
                      className="bg-red-600 hover:bg-red-700 text-white text-xs px-3 py-1"
                    >
                      {/* Only dispute if fully verified (admin verified OR 2+ confirmations) */}
                      {(benefit.is_admin_verified || (benefit.is_verified && benefit.confirmations_count >= 2)) ? 'Dispute' : 'Remove'}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="px-4 py-6 text-center text-gray-500">
          <p className="text-sm">No benefits in this status</p>
        </div>
      )}
    </div>
  )

  const totalConfirmations = benefits.reduce((sum, b) => sum + b.confirmations_count, 0)
  const verificationRate = benefits.length > 0 ?
    Math.round((verified.length / benefits.length) * 100) : 0

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Verification Overview</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{benefits.length}</div>
            <div className="text-sm text-gray-600">Total Benefits</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{verified.length}</div>
            <div className="text-sm text-gray-600">Verified</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-amber-600">{pending.length + partiallyVerified.length}</div>
            <div className="text-sm text-gray-600">Needs More Verification</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{verificationRate}%</div>
            <div className="text-sm text-gray-600">Verified Rate</div>
          </div>
        </div>
      </div>

      {/* Status Sections */}
      <div className="space-y-4">
        <StatusSection
          title="Verified Benefits"
          benefits={verified}
          icon={<CheckCircle className="w-4 h-4" />}
          color="text-green-600"
          bgColor="bg-green-50"
          description="Verified by administrators or community (2+ confirmations)"
        />

        <StatusSection
          title="Partially Verified"
          benefits={partiallyVerified}
          icon={<Clock className="w-4 h-4" />}
          color="text-orange-600"
          bgColor="bg-orange-50"
          description="Has 1 confirmation, needs 1 more to be verified"
        />

        <StatusSection
          title="Pending Verification"
          benefits={pending}
          icon={<Clock className="w-4 h-4" />}
          color="text-amber-600"
          bgColor="bg-amber-50"
          description="Awaiting verification from users"
        />
      </div>

      {/* Actions */}
      {canManage && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex flex-col sm:flex-row gap-3">
            <Button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white">
              Add New Benefits
            </Button>
            <Button variant="outline" className="flex-1">
              Bulk Verify Benefits
            </Button>
            <Button variant="outline" className="flex-1">
              Export Report
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
