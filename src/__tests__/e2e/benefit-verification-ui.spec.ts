/**
 * E2E Test for Benefit Verification UI Flow
 * Tests that verify button changes from 'Confirm' to 'Verified' and stays grayed out
 */

import { test, expect } from '@playwright/test'

test.describe('Benefit Verification UI Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('/')
  })

  test('should show Confirm Benefit button for unverified benefits and hide after verification', async ({ page }) => {
    // Step 1: Sign in as a user who can verify benefits using the E2E test token
    await page.goto('/auth/verify?token=mock-token')

    // Wait for successful sign-in
    await expect(page.locator('text=Sign Out')).toBeVisible({ timeout: 15000 })

    // Step 2: Navigate to a company page that has benefits
    await page.goto('/companies')

    // Find and click on a company (TechCorp should be available)
    await page.click('text=TechCorp')

    // Wait for company page to load
    await expect(page.locator('h1')).toContainText('TechCorp')

    // Step 3: Look for a benefit verification section
    const verificationSection = page.locator('[data-testid="benefit-verification"], .bg-gray-50:has-text("Verify this benefit")')

    if (await verificationSection.count() > 0) {
      // Step 4: Check if there's a Confirm Benefit button (unverified benefit)
      const confirmButton = verificationSection.locator('button:has-text("Confirm Benefit")')

      if (await confirmButton.count() > 0) {
        // Verify the button is enabled and has correct styling
        await expect(confirmButton).toBeEnabled()
        await expect(confirmButton).toHaveClass(/bg-blue-600/)

        // Step 5: Click the Confirm Benefit button to verify the benefit
        await confirmButton.click()

        // Step 6: Wait for the verification to complete and button to disappear
        // The new component returns null for verified benefits, so the button should disappear
        await expect(confirmButton).not.toBeVisible({ timeout: 10000 })

        // Step 7: Verify that the benefit is now in the verified section
        // Look for verified benefit indicators in the status-focused benefit management
        const verifiedSection = page.locator('[data-testid="verified-benefits"], .verified-benefits')
        if (await verifiedSection.count() > 0) {
          // Check that the benefit appears in the verified section
          await expect(verifiedSection).toBeVisible()
        }

        // Step 8: Reload the page to ensure the state persists
        await page.reload()
        await expect(page.locator('h1')).toContainText('TechCorp') // Wait for page to load

        // Step 9: Verify the benefit is still verified after reload
        // The Confirm Benefit button should not be visible for verified benefits
        const verificationSectionAfterReload = page.locator('[data-testid="benefit-verification"], .bg-gray-50:has-text("Verify this benefit")')
        if (await verificationSectionAfterReload.count() > 0) {
          const confirmButtonAfterReload = verificationSectionAfterReload.locator('button:has-text("Confirm Benefit")')
          await expect(confirmButtonAfterReload).not.toBeVisible()
        }

        console.log('✅ Benefit verification UI flow test completed successfully')
      } else {
        // If no Confirm Benefit button, the benefit may already be verified
        // In the new component, verified benefits return null, so no button is shown
        console.log('ℹ️ No Confirm Benefit button found - benefit may already be verified or user not authorized')
      }
    } else {
      console.log('ℹ️ No benefit verification sections found on this company page')
    }
  })

  test('should not show verification section for unauthorized users', async ({ page }) => {
    // Step 1: Sign in as a user from a different company using E2E test token
    await page.goto('/auth/verify?token=mock-token-2')

    // Wait for successful sign-in
    await expect(page.locator('text=Sign Out')).toBeVisible({ timeout: 15000 })

    // Step 2: Navigate to TechCorp company page (user is from Industries, not TechCorp)
    await page.goto('/companies')
    await page.click('text=TechCorp')

    // Wait for company page to load
    await expect(page.locator('h1')).toContainText('TechCorp')

    // Step 3: Verify that verification sections are not shown for unauthorized users
    const verificationSection = page.locator('[data-testid="benefit-verification"], .bg-gray-50:has-text("Verify this benefit")')

    // Should not see verification sections since user is not from TechCorp
    await expect(verificationSection).toHaveCount(0)

    console.log('✅ Unauthorized user correctly cannot see verification sections')
  })

  test('should handle verification API errors gracefully', async ({ page }) => {
    // Step 1: Sign in as authorized user using E2E test token

    // Wait for successful sign-in
    await expect(page.locator('text=Sign Out')).toBeVisible({ timeout: 15000 })

    // Step 2: Navigate to company page
    await page.goto('/companies')
    await page.click('text=Startup')
    await expect(page.locator('h1')).toContainText('Startup')

    // Step 3: Mock API failure by intercepting the verification request
    await page.route('/api/benefit-verifications', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      })
    })

    // Step 4: Try to verify a benefit
    const verificationSection = page.locator('[data-testid="benefit-verification"], .bg-gray-50:has-text("Verify this benefit")')

    if (await verificationSection.count() > 0) {
      const confirmButton = verificationSection.locator('button:has-text("Confirm Benefit")')

      if (await confirmButton.count() > 0) {
        await confirmButton.click()

        // Step 5: Verify error handling (button should remain visible)
        await page.waitForTimeout(2000) // Wait a bit for any potential changes

        // Button should still be "Confirm Benefit" and enabled
        await expect(confirmButton).toBeVisible()
        await expect(confirmButton).toBeEnabled()

        console.log('✅ API error handled gracefully - UI state preserved')
      }
    }
  })
})
