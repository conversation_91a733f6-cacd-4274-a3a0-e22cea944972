'use client'

import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { CheckCircle, Plus, Shield, Users, Clock, ChevronDown, ChevronUp } from 'lucide-react'
import { Button } from '@/components/ui/button'

import { CompanyVerificationNotice } from '@/components/company-verification-notice'
import { BatchBenefitSelection } from '@/components/batch-benefit-selection'

import { useCompanyAuthorization } from '@/hooks/use-company-authorization'
import { useBatchBenefitVerifications } from '@/hooks/use-batch-benefit-verifications'
import { useBatchUserVerificationStatus } from '@/hooks/use-batch-user-verification-status'
import { useBatchDisputeStatus } from '@/hooks/use-batch-dispute-status'

interface CompanyBenefit {
  id: string
  benefit_id: string
  name: string
  category: string
  icon?: string
  is_verified: boolean
  is_admin_verified?: boolean
  added_by?: string
  created_at: string
}

interface StatusFocusedBenefitManagementProps {
  companyId: string
  companyName: string
  canManage: boolean
}

export function StatusFocusedBenefitManagement({ companyId, companyName, canManage }: StatusFocusedBenefitManagementProps) {
  const [companyBenefits, setCompanyBenefits] = useState<CompanyBenefit[]>([])
  const [loading, setLoading] = useState(false)
  const [showBatchModal, setShowBatchModal] = useState(false)
  const [showDisputeForm, setShowDisputeForm] = useState<string | null>(null)
  const [isSubmittingDispute, setIsSubmittingDispute] = useState(false)
  const [disputeReasons, setDisputeReasons] = useState<Record<string, string>>({})
  const [showDisputeDetails, setShowDisputeDetails] = useState<string | null>(null)
  const [collapsedSections, setCollapsedSections] = useState<Record<string, boolean>>({
    'verified': false,
    'partially-verified': false,
    'pending': false
  })

  const toggleSection = useCallback((sectionId: string) => {
    setCollapsedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }))
  }, [])

  // Get all company benefit IDs for batch verification loading
  const companyBenefitIds = useMemo(() => companyBenefits.map(cb => cb.id), [companyBenefits])

  // Use company-level authorization
  const { authStatus, isLoading: isLoadingAuth } = useCompanyAuthorization(companyId)

  // Use batch benefit verifications for better performance
  const { getVerificationCounts, isLoading: isLoadingVerifications, refetch: refetchBatchVerificationCounts } = useBatchBenefitVerifications(companyBenefitIds)

  // Use batch user verification status for better performance
  const { getVerificationStatus, refetch: refetchBatchUserVerifications } = useBatchUserVerificationStatus(companyBenefitIds)

  // Use batch dispute status for better performance
  const { getDisputeStatus, isLoading: isLoadingDisputeStatus, refetch: refetchBatchDisputeStatus } = useBatchDisputeStatus(companyBenefitIds)

  const fetchCompanyBenefits = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/companies/${companyId}/benefits`)
      if (response.ok) {
        const data = await response.json()
        setCompanyBenefits(data)
      }
    } catch (error) {
      console.error('Error fetching company benefits:', error)
    } finally {
      setLoading(false)
    }
  }, [companyId])

  useEffect(() => {
    fetchCompanyBenefits()
  }, [companyId, fetchCompanyBenefits])



  const handleBatchSuccess = () => {
    fetchCompanyBenefits()
    setShowBatchModal(false)
  }

  const handleRemoveBenefit = async (benefitId: string, benefitName: string, isVerified: boolean, companyBenefitId: string) => {
    const verificationCounts = getVerificationCounts(companyBenefitId)
    const confirmationsCount = verificationCounts?.confirmed || 0

    // Get the benefit to check if it's admin verified
    const benefit = companyBenefits.find(b => b.id === companyBenefitId)
    const isAdminVerified = benefit?.is_admin_verified || false
    const isFullyVerified = isAdminVerified || (isVerified && confirmationsCount >= 2)

    // For fully verified benefits (admin verified OR 2+ confirmations), show dispute form
    if (isFullyVerified) {
      setShowDisputeForm(companyBenefitId)
      setDisputeReasons(prev => ({ ...prev, [companyBenefitId]: '' }))
      return
    }

    // For unverified or partially verified benefits, delete directly
    if (!confirm(`Remove "${benefitName}" from ${companyName}? This will also delete all verifications.`)) {
      return
    }

    try {
      const response = await fetch(`/api/companies/${companyId}/benefits?benefitId=${benefitId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        alert('Benefit removed successfully!')
        fetchCompanyBenefits()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to remove benefit')
      }
    } catch (error) {
      console.error('Error removing benefit:', error)
      alert('Failed to remove benefit')
    }
  }

  const handleSubmitDispute = async (companyBenefitId: string, reason: string) => {
    setIsSubmittingDispute(true)
    try {
      const response = await fetch('/api/benefit-removal-disputes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          companyBenefitId: companyBenefitId,
          reason: reason
        })
      })

      if (response.ok) {
        alert('Dispute submitted successfully!')
        setShowDisputeForm(null)
        setDisputeReasons(prev => ({ ...prev, [companyBenefitId]: '' }))
        // Refresh dispute info and benefits
        await refetchBatchDisputeStatus()
        fetchCompanyBenefits()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to submit dispute')
      }
    } catch (error) {
      console.error('Error submitting dispute:', error)
      alert('Failed to submit dispute')
    } finally {
      setIsSubmittingDispute(false)
    }
  }

  const handleCancelDispute = async (disputeId: string) => {
    try {
      const response = await fetch(`/api/benefit-removal-disputes/${disputeId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'cancelled' })
      })

      if (response.ok) {
        setShowDisputeDetails(null)
        // Refresh data
        await Promise.all([
          refetchBatchDisputeStatus(),
          fetchCompanyBenefits()
        ])
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to cancel dispute')
      }
    } catch (error) {
      console.error('Error canceling dispute:', error)
      alert('Failed to cancel dispute')
    }
  }

  // Stable handler for dispute form cancel
  const handleDisputeFormCancel = useCallback((benefitId: string) => {
    setShowDisputeForm(null)
    setDisputeReasons(prev => ({ ...prev, [benefitId]: '' }))
  }, [])

  const handleVerifyBenefit = async (companyBenefitId: string) => {
    try {
      const response = await fetch('/api/benefit-verifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          companyBenefitId: companyBenefitId,
          status: 'confirmed'
        })
      })

      if (response.ok) {
        // Force refetch all data to ensure UI updates correctly
        await Promise.all([
          refetchBatchUserVerifications(),
          refetchBatchVerificationCounts(),
          fetchCompanyBenefits()
        ])

        // Small delay to ensure all data is updated
        setTimeout(() => {
          fetchCompanyBenefits()
        }, 500)
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to verify benefit')
      }
    } catch (error) {
      console.error('Error verifying benefit:', error)
      alert('Failed to verify benefit')
    }
  }

  // Group benefits by verification status
  const verified = companyBenefits.filter(b => {
    if (b.is_admin_verified) return true
    const verificationCounts = getVerificationCounts(b.id)
    const confirmationsCount = verificationCounts?.confirmed || 0
    return b.is_verified && confirmationsCount >= 2
  })

  const partiallyVerified = companyBenefits.filter(b => {
    if (b.is_admin_verified) return false
    const verificationCounts = getVerificationCounts(b.id)
    const confirmationsCount = verificationCounts?.confirmed || 0
    return confirmationsCount === 1
  })

  const pending = companyBenefits.filter(b => {
    if (b.is_admin_verified) return false
    const verificationCounts = getVerificationCounts(b.id)
    const confirmationsCount = verificationCounts?.confirmed || 0
    return confirmationsCount === 0
  })

  // Get existing benefit IDs for the batch selection component
  const existingBenefitIds = companyBenefits.map(cb => cb.benefit_id)

  const StatusSection = ({
    title,
    benefits,
    icon,
    color,
    bgColor,
    description,
    sectionId,
    isCollapsible = true
  }: {
    title: string
    benefits: CompanyBenefit[]
    icon: React.ReactNode
    color: string
    bgColor: string
    description: string
    sectionId: string
    isCollapsible?: boolean
  }) => {
    const isCollapsed = collapsedSections[sectionId]

    return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      {/* Section Header */}
      <div
        className={`${bgColor} px-4 py-3 border-b border-gray-200 ${isCollapsible ? 'cursor-pointer hover:opacity-80 transition-opacity' : ''}`}
        onClick={isCollapsible ? () => toggleSection(sectionId) : undefined}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={`${color}`}>{icon}</div>
            <div>
              <h4 className="text-sm font-semibold text-gray-900">{title}</h4>
              <p className="text-xs text-gray-600">{description}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`${color} bg-white px-2 py-1 rounded-full`}>
              <span className="text-sm font-bold">{benefits.length}</span>
            </div>
            {isCollapsible && (
              <div className={`${color} transition-transform duration-200 ${isCollapsed ? 'rotate-0' : 'rotate-180'}`}>
                <ChevronDown className="w-4 h-4" />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Benefits List */}
      {!isCollapsed && benefits.length > 0 ? (
        <div className="divide-y divide-gray-100">
          {benefits.map((benefit) => {
            const verificationCounts = getVerificationCounts(benefit.id)
            const userVerificationStatus = getVerificationStatus(benefit.id)
            const confirmationsCount = verificationCounts?.confirmed || 0
            
            return (
              <div key={benefit.id} className="px-4 py-3 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <span className="text-lg flex-shrink-0">{benefit.icon || '🎯'}</span>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h5 className="text-sm font-medium text-gray-900 truncate">{benefit.name}</h5>
                        {/* Verification type indicator */}
                        {benefit.is_admin_verified ? (
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <Shield className="w-3 h-3 mr-1" />
                            Admin
                          </span>
                        ) : benefit.is_verified && confirmationsCount >= 2 ? (
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Community
                          </span>
                        ) : null}
                      </div>
                      <div className="flex items-center space-x-3 mt-1">
                        <span className="text-xs text-gray-500 capitalize">
                          {benefit.category?.replace('_', ' ') || 'Other'}
                        </span>
                        {confirmationsCount > 0 && (
                          <div className="flex items-center space-x-1 text-gray-600">
                            <Users className="w-3 h-3" />
                            <span className="text-xs">{confirmationsCount} confirmations</span>
                          </div>
                        )}
                        {/* User's personal verification status */}
                        {!benefit.is_admin_verified && (
                          <span className={`text-xs px-2 py-0.5 rounded-full ${
                            userVerificationStatus?.hasVerified
                              ? 'bg-green-50 text-green-700'
                              : 'bg-gray-50 text-gray-600'
                          }`}>
                            {userVerificationStatus?.hasVerified ? '✓ You verified' : 'Not verified by you'}
                          </span>
                        )}

                        {/* Dispute information for verified benefits */}
                        {(benefit.is_admin_verified || (benefit.is_verified && confirmationsCount >= 2)) && (() => {
                          const disputeStatus = getDisputeStatus(benefit.id)
                          if (!disputeStatus) return null

                          return (
                            <>
                              {/* {disputeStatus.stats && disputeStatus.stats.total > 0 && (
                                <span className="text-xs text-gray-500">
                                  {disputeStatus.stats.pending > 0 && `${disputeStatus.stats.pending} pending dispute`}
                                  {disputeStatus.stats.approved > 0 && ` ${disputeStatus.stats.approved} approved dispute`}
                                </span>
                              )} */}
                              {/* {disputeStatus.dispute && (
                                <span className={`text-xs px-2 py-0.5 rounded-full ${
                                  disputeStatus.dispute.status === 'pending' ? 'bg-yellow-50 text-yellow-700' :
                                  disputeStatus.dispute.status === 'approved' ? 'bg-red-50 text-red-700' :
                                  disputeStatus.dispute.status === 'rejected' ? 'bg-gray-50 text-gray-700' :
                                  'bg-gray-50 text-gray-600'
                                }`}>
                                  {disputeStatus.dispute.status === 'pending' ? '⏳ Dispute pending' :
                                   disputeStatus.dispute.status === 'approved' ? '✓ Dispute approved' :
                                   disputeStatus.dispute.status === 'rejected' ? '✗ Dispute rejected' :
                                   '📋 Dispute submitted'}
                                </span>
                              )} */}
                            </>
                          )
                        })()}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    {/* Show verify button for non-admin verified benefits where user hasn't verified */}
                    {!benefit.is_admin_verified && !userVerificationStatus?.hasVerified && !isLoadingAuth && authStatus?.authorized && (
                      <Button
                        size="sm"
                        onClick={() => handleVerifyBenefit(benefit.id)}
                        className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1"
                      >
                        Verify
                      </Button>
                    )}

                    {canManage && (() => {
                      const disputeStatus = getDisputeStatus(benefit.id)
                      const hasPendingDispute = disputeStatus?.dispute && disputeStatus.dispute.status === 'pending'
                      const isFullyVerified = benefit.is_admin_verified || (benefit.is_verified && confirmationsCount >= 2)



                      return (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            if (hasPendingDispute) {
                              setShowDisputeDetails(benefit.id)
                            } else {
                              handleRemoveBenefit(benefit.benefit_id, benefit.name, Boolean(benefit.is_verified || benefit.is_admin_verified), benefit.id)
                            }
                          }}
                          className="bg-red-600 hover:bg-red-700 text-white text-xs px-3 py-1"
                        >
                          {hasPendingDispute ? 'View Dispute' : isFullyVerified ? 'Dispute' : 'Remove'}
                        </Button>
                      )
                    })()}
                  </div>
                </div>

                {/* Inline Dispute Form */}
                {showDisputeForm === benefit.id && (
                  <DisputeForm
                    benefitId={benefit.id}
                    benefitName={benefit.name}
                    companyName={companyName}
                    onSubmit={handleSubmitDispute}
                    onCancel={() => handleDisputeFormCancel(benefit.id)}
                    isSubmitting={isSubmittingDispute}
                    initialValue={disputeReasons[benefit.id] || ''}
                  />
                )}

                {/* Dispute Details Modal */}
                {showDisputeDetails === benefit.id && (() => {
                  const disputeStatus = getDisputeStatus(benefit.id)
                  if (!disputeStatus?.dispute) return null

                  return (
                    <DisputeDetails
                      dispute={disputeStatus.dispute}
                      benefitName={benefit.name}
                      onClose={() => setShowDisputeDetails(null)}
                      onCancel={async (disputeId) => {
                        try {
                          const response = await fetch('/api/benefit-removal-disputes/cancel', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ disputeId })
                          })

                          if (response.ok) {
                            alert('Dispute cancelled successfully!')
                            setShowDisputeDetails(null)
                            await refetchBatchDisputeStatus()
                            fetchCompanyBenefits()
                          } else {
                            const error = await response.json()
                            alert(error.error || 'Failed to cancel dispute')
                          }
                        } catch (error) {
                          console.error('Error cancelling dispute:', error)
                          alert('Failed to cancel dispute')
                        }
                      }}
                    />
                  )
                })()}
              </div>
            )
          })}
        </div>
      ) : (
        <div className="px-4 py-6 text-center text-gray-500">
          <p className="text-sm">No benefits in this status</p>
        </div>
      )}
    </div>
  )
  }
  const verificationRate = companyBenefits.length > 0 ? 
    Math.round((verified.length / companyBenefits.length) * 100) : 0

  return (
    <div className="space-y-6">
      {/* Page Header with Actions */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Company Benefits</h2>
          <p className="text-sm text-gray-600 mt-1">
            {canManage
              ? `Manage and verify benefits for ${companyName}. You can add, remove, and verify benefits based on your company email domain.`
              : `View and verify benefits offered by ${companyName}. You can confirm or dispute benefits based on your experience.`
            }
          </p>
        </div>

        {canManage && (
          <Button
            onClick={() => setShowBatchModal(true)}
            className="flex items-center gap-2 w-full sm:w-auto flex-shrink-0"
          >
            <Plus className="w-4 h-4" />
            Add Benefits
          </Button>
        )}
      </div>

      {(loading || isLoadingVerifications || isLoadingDisputeStatus) ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 mt-2">
            {loading ? 'Loading benefits...' :
             isLoadingVerifications ? 'Loading verification data...' :
             'Loading dispute information...'}
          </p>
        </div>
      ) : companyBenefits.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No benefits have been added for this company yet.</p>
          {canManage && (
            <Button
              onClick={() => setShowBatchModal(true)}
              className="mt-4 flex items-center gap-2 mx-auto w-full sm:w-auto"
            >
              <Plus className="w-4 h-4" />
              Add Benefits
            </Button>
          )}
        </div>
      ) : (
        <>
          {/* Overview Stats */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Verification Overview</h3>
              <p className="text-sm text-gray-600 mt-1">
                Track the verification status of all benefits for {companyName}.
              </p>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{companyBenefits.length}</div>
                <div className="text-sm text-gray-600">Total Benefits</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{verified.length}</div>
                <div className="text-sm text-gray-600">Verified</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-amber-600">{pending.length + partiallyVerified.length}</div>
                <div className="text-sm text-gray-600">Needs More Verification</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{verificationRate}%</div>
                <div className="text-sm text-gray-600">Verified Rate</div>
              </div>
            </div>
          </div>

          {/* Status Sections */}
          <div className="space-y-4">
            <StatusSection
              title="Verified Benefits"
              benefits={verified}
              icon={<CheckCircle className="w-4 h-4" />}
              color="text-green-600"
              bgColor="bg-green-50"
              description="Verified by administrators or community (2+ confirmations)"
              sectionId="verified"
            />

            <StatusSection
              title="Partially Verified"
              benefits={partiallyVerified}
              icon={<Clock className="w-4 h-4" />}
              color="text-orange-600"
              bgColor="bg-orange-50"
              description="Has 1 confirmation, needs 1 more to be verified"
              sectionId="partially-verified"
            />

            <StatusSection
              title="Pending Verification"
              benefits={pending}
              icon={<Clock className="w-4 h-4" />}
              color="text-amber-600"
              bgColor="bg-amber-50"
              description="Awaiting verification from users"
              sectionId="pending"
            />
          </div>

          {/* Company-level verification notice */}
          {!isLoadingAuth && authStatus && !authStatus.authorized && (
            <div className="mt-8">
              <CompanyVerificationNotice authStatus={authStatus} />
            </div>
          )}
        </>
      )}

      {/* Batch Benefit Selection Modal */}
      <BatchBenefitSelection
        companyId={companyId}
        companyName={companyName}
        isOpen={showBatchModal}
        onClose={() => setShowBatchModal(false)}
        onSuccess={handleBatchSuccess}
        existingBenefitIds={existingBenefitIds}
      />
    </div>
  )
}

// Separate component for dispute form to prevent re-rendering issues
interface DisputeFormProps {
  benefitId: string
  benefitName: string
  companyName: string
  onSubmit: (benefitId: string, reason: string) => void
  onCancel: () => void
  isSubmitting: boolean
  initialValue?: string
}

const DisputeForm = React.memo(function DisputeForm({ benefitId, benefitName, companyName, onSubmit, onCancel, isSubmitting, initialValue = '' }: DisputeFormProps) {
  const [reason, setReason] = useState(initialValue)

  const handleSubmit = useCallback(() => {
    if (reason.trim()) {
      onSubmit(benefitId, reason.trim())
    }
  }, [benefitId, reason, onSubmit])

  return (
    <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
      <h5 className="font-medium text-gray-900 mb-2">
        Request Removal of "{benefitName}"
      </h5>
      <p className="text-sm text-gray-600 mb-3">
        Please explain why this benefit should be removed from {companyName}:
      </p>
      <textarea
        value={reason}
        onChange={(e) => setReason(e.target.value)}
        placeholder="Explain why this benefit should be removed..."
        className="w-full p-3 border border-gray-300 rounded-md text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        rows={2}
      />
      <div className="flex gap-2 mt-3">
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting || !reason.trim()}
          className="bg-yellow-600 hover:bg-yellow-700 text-white"
        >
          {isSubmitting ? 'Submitting...' : 'Submit Dispute'}
        </Button>
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
      </div>
    </div>
  )
})

// Separate component for dispute details
interface DisputeDetailsProps {
  dispute: {
    id: string
    reason: string
    status: string
    created_at: string
    admin_comment?: string
  }
  benefitName: string
  onClose: () => void
  onCancel: (disputeId: string) => void
}

function DisputeDetails({ dispute, benefitName, onClose, onCancel }: DisputeDetailsProps) {
  return (
    <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <div className="flex items-center justify-between mb-3">
        <h5 className="font-medium text-gray-900">
          Your Dispute for "{benefitName}"
        </h5>
        <Button variant="outline" size="sm" onClick={onClose}>
          ✕
        </Button>
      </div>

      <div className="space-y-3">
        <div>
          <span className="text-sm font-medium text-gray-700">Status: </span>
          <span className={`text-sm px-2 py-1 rounded-full ${
            dispute.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
            dispute.status === 'approved' ? 'bg-green-100 text-green-800' :
            dispute.status === 'rejected' ? 'bg-red-100 text-red-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {dispute.status.charAt(0).toUpperCase() + dispute.status.slice(1)}
          </span>
        </div>

        <div>
          <span className="text-sm font-medium text-gray-700">Submitted: </span>
          <span className="text-sm text-gray-600">
            {new Date(dispute.created_at).toLocaleDateString()}
          </span>
        </div>

        <div>
          <span className="text-sm font-medium text-gray-700">Your reason: </span>
          <p className="text-sm text-gray-600 mt-1 p-2 bg-white rounded border">
            {dispute.reason}
          </p>
        </div>

        {dispute.admin_comment && (
          <div>
            <span className="text-sm font-medium text-gray-700">Admin response: </span>
            <p className="text-sm text-gray-600 mt-1 p-2 bg-white rounded border">
              {dispute.admin_comment}
            </p>
          </div>
        )}

        {dispute.status === 'pending' && (
          <div className="pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onCancel(dispute.id)}
              className="text-red-600 border-red-300 hover:bg-red-50"
            >
              Cancel Dispute
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
