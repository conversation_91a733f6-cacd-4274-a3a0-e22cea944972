'use client'

import { useState } from 'react'
import { MinimalRowList } from '../../../design-examples/minimal-row-list'
import { GroupedCategoryList } from '../../../design-examples/grouped-category-list'
import { StatusFocusedBenefitManagement } from '@/components/status-focused-benefit-management'
import { ActionOrientedList } from '../../../design-examples/action-oriented-list'
import { CompactGridList } from '../../../design-examples/compact-grid-list'

// Mock data for demonstration
const mockBenefits = [
  {
    id: '1',
    name: 'Bike Leasing (Dienstradleasing)',
    icon: '🚴',
    is_verified: false,
    is_admin_verified: false,
    confirmations_count: 0,
    category: 'transport',
    user_has_verified: false
  },
  {
    id: '2',
    name: 'Gym Membership (Fitnessstudio-Mitgliedschaft)',
    icon: '🏋️',
    is_verified: true,
    is_admin_verified: false,
    confirmations_count: 3,
    category: 'health',
    user_has_verified: true
  },
  {
    id: '3',
    name: 'Sports Programs (Sportprogramme)',
    icon: '⚽',
    is_verified: true,
    is_admin_verified: false,
    confirmations_count: 2,
    category: 'health',
    user_has_verified: false
  },
  {
    id: '4',
    name: 'Wellness Programs (Wellness-Programme)',
    icon: '🧘',
    is_verified: false,
    is_admin_verified: true,
    confirmations_count: 0,
    category: 'wellness',
    user_has_verified: false
  },
  {
    id: '5',
    name: 'Company Car (Firmenwagen)',
    icon: '🚗',
    is_verified: true,
    is_admin_verified: false,
    confirmations_count: 4,
    category: 'transport',
    user_has_verified: false
  },
  {
    id: '6',
    name: 'Training Budget (Weiterbildungsbudget)',
    icon: '📚',
    is_verified: true,
    is_admin_verified: false,
    confirmations_count: 1,
    category: 'learning',
    user_has_verified: false
  },
  {
    id: '7',
    name: 'Health Insurance (Krankenversicherung)',
    icon: '🏥',
    is_verified: false,
    is_admin_verified: true,
    confirmations_count: 0,
    category: 'health',
    user_has_verified: false
  },
  {
    id: '9',
    name: 'Flexible Working Hours (Flexible Arbeitszeiten)',
    icon: '⏰',
    is_verified: false,
    is_admin_verified: false,
    confirmations_count: 0,
    category: 'other',
    user_has_verified: false
  },
  {
    id: '8',
    name: 'Pension Plan (Betriebsrente)',
    icon: '💰',
    is_verified: true,
    is_admin_verified: false,
    confirmations_count: 5,
    category: 'financial',
    user_has_verified: true
  }
]

const designs = [
  { id: 'minimal', name: 'Minimal Row-Based List', component: MinimalRowList },
  { id: 'grouped', name: 'Grouped Category List', component: GroupedCategoryList },
  { id: 'status', name: 'Status-Focused List (PRODUCTION)', component: StatusFocusedBenefitManagement },
  { id: 'action', name: 'Action-Oriented List', component: ActionOrientedList },
  { id: 'grid', name: 'Compact Grid List', component: CompactGridList }
]

export default function DesignPreviewPage() {
  const [selectedDesign, setSelectedDesign] = useState('minimal')
  
  const mockHandlers = {
    onVerify: (id: string) => alert(`Verify benefit ${id}`),
    onDeleteOrDispute: (id: string) => {
      const benefit = mockBenefits.find(b => b.id === id)
      if (benefit?.is_verified || benefit?.is_admin_verified) {
        alert(`Dispute benefit ${id}`)
      } else {
        alert(`Remove benefit ${id}`)
      }
    }
  }

  const selectedDesignData = designs.find(d => d.id === selectedDesign)

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h1 className="text-2xl font-bold text-gray-900">Benefits Overview Design Examples</h1>
          <p className="text-gray-600 mt-2">Compare different list-style designs for the company dashboard</p>
        </div>
      </div>

      {/* Design Selector */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Select Design to Preview</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-3">
            {designs.map((design) => (
              <button
                key={design.id}
                onClick={() => setSelectedDesign(design.id)}
                className={`p-3 rounded-lg border text-sm font-medium transition-colors ${
                  selectedDesign === design.id
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                {design.name}
              </button>
            ))}
          </div>
        </div>

        {/* Design Preview */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="mb-4">
            <h3 className="text-xl font-semibold text-gray-900">
              {selectedDesignData?.name}
            </h3>
            <p className="text-gray-600 text-sm mt-1">
              Preview with {mockBenefits.length} sample benefits
            </p>
          </div>

          {selectedDesignData && (
            selectedDesign === 'status' ? (
              <StatusFocusedBenefitManagement
                companyId="mock-company-id"
                companyName="Mock Company"
                canManage={true}
              />
            ) : selectedDesign === 'minimal' ? (
              <MinimalRowList
                benefits={mockBenefits}
                canManage={true}
                onVerify={mockHandlers.onVerify}
                onDeleteOrDispute={mockHandlers.onDeleteOrDispute}
              />
            ) : selectedDesign === 'grouped' ? (
              <GroupedCategoryList
                benefits={mockBenefits}
                canManage={true}
                onVerify={mockHandlers.onVerify}
                onDeleteOrDispute={mockHandlers.onDeleteOrDispute}
              />
            ) : selectedDesign === 'action' ? (
              <ActionOrientedList
                benefits={mockBenefits}
                canManage={true}
                onVerify={mockHandlers.onVerify}
                onDeleteOrDispute={mockHandlers.onDeleteOrDispute}
              />
            ) : selectedDesign === 'grid' ? (
              <CompactGridList
                benefits={mockBenefits}
                canManage={true}
                onVerify={mockHandlers.onVerify}
                onDeleteOrDispute={mockHandlers.onDeleteOrDispute}
              />
            ) : null
          )}
        </div>
      </div>
    </div>
  )
}
